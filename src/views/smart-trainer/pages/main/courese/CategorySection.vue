<template>
    <div class="course-category-section">
        <swiper
            :modules="[Autoplay]"
            :slides-per-view="2"
            :space-between="12"
            :autoplay="{
                delay: 4000,
                disableOnInteraction: false
            }"
            class="category-swiper"
        >
            <swiper-slide v-for="category in categories" :key="category.key">
                <div
                    class="category-card"
                    :style="{ backgroundColor: category.backgroundColor }"
                    @click="handleCategoryClick(category.key)"
                >
                    <!-- 顶部大黑字标题 -->
                    <div class="card-main-title">{{ category.title }}</div>

                    <!-- 带颜色的介绍 -->
                    <p class="card-colored-intro" :style="{ color: category.introColor }">
                        {{ category.subtitle }}
                    </p>

                    <!-- 主要内容区域 -->
                    <div class="card-main-content">
                        <ul class="feature-list">
                            <li
                                v-for="feature in category.features"
                                :key="feature"
                                class="feature-item"
                            >
                                • {{ feature }}
                            </li>
                        </ul>
                    </div>

                    <!-- 右侧图标 -->
                    <div class="card-icon">
                        <img
                            :src="category.image"
                            :alt="category.title"
                            class="category-image"
                        />
                    </div>
                </div>
            </swiper-slide>
        </swiper>
    </div>
</template>

<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay } from 'swiper/modules';

// 引入 Swiper 样式
import 'swiper/css';

// 定义 props
const props = defineProps({
    categories: {
        type: Array,
        required: true,
        default: () => []
    }
});

// 定义 emits
const emit = defineEmits(['categoryClick']);

/**
 * 处理课程分类点击
 * @param {string} categoryKey - 分类键值
 */
const handleCategoryClick = (categoryKey) => {
    emit('categoryClick', categoryKey);
};
</script>

<style lang="scss" scoped>
.course-category-section {
    margin-bottom: 14px;

    .category-swiper {
        .swiper-slide {
            height: auto;
        }

        .category-card {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 12px;
            padding: 10px 14px 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;

            .card-main-title {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin-bottom: 2px;
            }

            .card-colored-intro {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 6px;
            }

            .card-main-content {
                .feature-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    .feature-item {
                        font-size: 12px;
                        color: #666;
                        line-height: 1.5;
                        margin-bottom: 4px;
                    }
                }
            }

            .card-icon {
                position: absolute;
                bottom: 10px;
                right: 10px;
                width: 30px;
                height: 30px;

                .category-image {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
    }
}
</style>
